import {
  Controller,
  Post,
  Body,
  UseFilters,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { SendMessageUseCase } from '../../application/use-cases/chat/send-message.use-case';
import { HttpExceptionFilter } from '../filters';

export class SendMessageRequest {
  message: string;
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
}

export class SendMessageResponse {
  message: string;
  conversationId?: string;
}

@Controller('chat')
@UseFilters(HttpExceptionFilter)
export class ChatController {
  constructor(private readonly sendMessageUseCase: SendMessageUseCase) {}

  @Post('message')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async sendMessage(@Body() request: SendMessageRequest): Promise<SendMessageResponse> {
    const response = await this.sendMessageUseCase.execute({
      message: request.message,
      conversationHistory: request.conversationHistory,
    });

    return {
      message: response.message,
      conversationId: response.conversationId,
    };
  }
}
