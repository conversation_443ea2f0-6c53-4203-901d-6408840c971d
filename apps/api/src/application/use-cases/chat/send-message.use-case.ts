import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

export interface SendMessageRequest {
  message: string;
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
}

export interface SendMessageResponse {
  message: string;
  conversationId?: string;
}

@Injectable()
export class SendMessageUseCase {
  private openai: OpenAI;

  constructor(private configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is not configured');
    }

    this.openai = new OpenAI({
      apiKey,
    });
  }

  async execute(request: SendMessageRequest): Promise<SendMessageResponse> {
    try {
      const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: `You are a specialized AI hiring assistant for Checku, a professional recruitment and job platform.
          Your expertise is exclusively in:

          - Job searching and career advice
          - Resume and CV optimization
          - Interview preparation and tips
          - Salary negotiation guidance
          - Career development and growth
          - Industry insights and job market trends
          - Professional networking advice
          - Skills development recommendations
          - Job application strategies
          - Workplace culture and fit assessment

          IMPORTANT GUIDELINES:
          - Only discuss topics related to jobs, careers, hiring, and professional development
          - If users ask about non-job related topics, politely redirect them back to career/hiring discussions
          - Be professional, encouraging, and supportive
          - Provide actionable, practical advice
          - Keep responses concise but informative
          - Ask follow-up questions to better understand their career goals

          If someone asks about something unrelated to jobs/careers, respond with something like:
          "I'm specialized in helping with job searches, career development, and hiring-related questions. How can I assist you with your career goals today?"

          Always maintain a professional, helpful tone focused on advancing their career success.`,
        },
        ...(request.conversationHistory || []).map((msg) => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
        })),
        {
          role: 'user',
          content: request.message,
        },
      ];

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages,
        max_tokens: 500,
        temperature: 0.7,
      });

      const responseMessage = completion.choices[0]?.message?.content;

      if (!responseMessage) {
        throw new Error('No response from OpenAI');
      }

      return {
        message: responseMessage,
      };
    } catch (error) {
      console.error('OpenAI API Error:', error);
      throw new Error('Failed to get response from AI assistant');
    }
  }
}
